# 存摺分析功能說明

## 📋 功能概述

新增的存摺分析功能使用 Gemini API 來辨識存摺圖片，提取以下資訊：
- **帳戶名稱** (戶名)
- **存摺帳號** (帳號)

## 🚀 使用方式

### 1. API 端點

**POST** `/api/v1/analyze/passbook`

### 2. 請求格式

```bash
curl -X POST \
  -F 'image=@存摺圖片.jpg' \
  http://127.0.0.1:8008/api/v1/analyze/passbook
```

### 3. 回應格式

```json
{
  "success": true,
  "request_id": "uuid-string",
  "data": {
    "request_id": "uuid-string",
    "filename": "存摺圖片.jpg",
    "file_size": 123456,
    "account_name": "王小明",
    "account_number": "*************",
    "confidence": 0.95,
    "timestamp": "2025-07-29T12:34:56.789012",
    "source": "gemini"
  }
}
```

### 4. 錯誤回應

```json
{
  "success": false,
  "error": "錯誤訊息",
  "code": "ERROR_CODE"
}
```

## 🛠️ 測試工具

### 1. 簡單測試
```bash
python test_passbook_simple.py
```

### 2. 完整測試（需要存摺圖片）
```bash
python test_passbook_api.py 存摺圖片.jpg
```

### 3. 命令行工具
```bash
python passbook_analyzer.py 存摺圖片.jpg --output result.json
```

## 📁 相關檔案

- `passbook_analyzer.py` - 存摺分析器核心類別
- `app.py` - API 服務器（已添加存摺分析端點）
- `test_passbook_api.py` - 完整的 API 測試腳本
- `test_passbook_simple.py` - 簡單的端點測試腳本

## 🔧 技術細節

### 支援的圖片格式
- JPG/JPEG
- PNG
- WEBP

### 檔案大小限制
- 最大 16MB

### AI 模型
- 使用 Google Gemini 1.5 Flash 模型
- 需要設定 `GEMINI_API_KEY` 環境變數

### 圖片預處理
- 自動轉換為 RGB 格式
- 自動調整大小（最大 2048x2048）
- 圖片驗證和優化

## 🔍 分析流程

1. **圖片驗證** - 檢查檔案格式和大小
2. **圖片預處理** - 格式轉換和大小調整
3. **AI 分析** - 使用 Gemini API 提取文字資訊
4. **結果解析** - 解析 JSON 回應並驗證資料
5. **回應格式化** - 統一回應格式

## 📊 健康檢查

檢查存摺分析器狀態：
```bash
curl http://127.0.0.1:8008/health
```

回應中的 `services.passbook_analyzer` 欄位顯示分析器狀態。

## 🎯 使用範例

### Python 範例
```python
import requests

# 上傳存摺圖片進行分析
with open('存摺.jpg', 'rb') as f:
    files = {'image': f}
    response = requests.post(
        'http://127.0.0.1:8008/api/v1/analyze/passbook',
        files=files
    )
    
if response.status_code == 200:
    result = response.json()
    data = result['data']
    print(f"帳戶名稱: {data['account_name']}")
    print(f"存摺帳號: {data['account_number']}")
    print(f"信心度: {data['confidence']:.2%}")
else:
    print(f"分析失敗: {response.status_code}")
```

### JavaScript 範例
```javascript
const formData = new FormData();
formData.append('image', fileInput.files[0]);

fetch('http://127.0.0.1:8008/api/v1/analyze/passbook', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => {
    if (data.success) {
        console.log('帳戶名稱:', data.data.account_name);
        console.log('存摺帳號:', data.data.account_number);
        console.log('信心度:', data.data.confidence);
    } else {
        console.error('分析失敗:', data.error);
    }
});
```

## 🔐 環境設定

確保已設定以下環境變數：
```bash
export GEMINI_API_KEY="your_gemini_api_key_here"
```

或在 `.env` 檔案中：
```
GEMINI_API_KEY=your_gemini_api_key_here
```

## 📈 效能考量

- 單次請求處理時間：約 2-5 秒
- 支援並發請求
- 自動清理暫存檔案
- 記憶體使用優化

## 🐛 故障排除

### 常見錯誤

1. **ModuleNotFoundError: No module named 'google.generativeai'**
   - 解決：安裝 Google Generative AI 套件
   ```bash
   pip install google-generativeai
   ```

2. **未設定 GEMINI_API_KEY**
   - 解決：設定環境變數或 .env 檔案

3. **圖片格式不支援**
   - 解決：使用 JPG、PNG 或 WEBP 格式

4. **檔案過大**
   - 解決：壓縮圖片至 16MB 以下

### 日誌檢查
```bash
tail -f logs/api_server.log
```

## 🔄 更新記錄

- **v2.0.0** - 新增存摺分析功能
  - 添加 `/api/v1/analyze/passbook` 端點
  - 支援帳戶名稱和存摺帳號提取
  - 整合 Gemini API
  - 添加完整的測試工具
