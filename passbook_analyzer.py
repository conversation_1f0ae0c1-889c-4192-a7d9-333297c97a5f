#!/usr/bin/env python3
"""
存摺分析器
使用 Gemini API 從存摺圖片中提取帳戶名稱和存摺帳號資訊
"""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from PIL import Image
import google.generativeai as genai

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PassbookAnalyzer:
    """存摺分析器類別"""
    
    def __init__(self):
        """初始化分析器"""
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.gemini_model = None
        
        if self.gemini_api_key:
            try:
                genai.configure(api_key=self.gemini_api_key)
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                logger.info("Gemini API 初始化成功")
            except Exception as e:
                logger.error(f"Gemini API 初始化失敗: {e}")
                self.gemini_model = None
        else:
            logger.warning("未設定 GEMINI_API_KEY")
    
    def validate_image(self, image_path: str) -> bool:
        """
        驗證圖片檔案
        
        Args:
            image_path: 圖片檔案路徑
            
        Returns:
            bool: 驗證是否通過
        """
        try:
            if not os.path.exists(image_path):
                logger.error(f"圖片檔案不存在: {image_path}")
                return False
            
            # 檢查檔案大小 (最大 16MB)
            file_size = os.path.getsize(image_path)
            if file_size > 16 * 1024 * 1024:
                logger.error(f"圖片檔案過大: {file_size / 1024 / 1024:.2f}MB")
                return False
            
            # 嘗試開啟圖片
            with Image.open(image_path) as img:
                img.verify()
            
            logger.info(f"圖片驗證通過: {image_path}")
            return True
            
        except Exception as e:
            logger.error(f"圖片驗證失敗: {e}")
            return False
    
    def preprocess_image(self, image_path: str) -> Optional[Image.Image]:
        """
        預處理圖片
        
        Args:
            image_path: 圖片檔案路徑
            
        Returns:
            處理後的圖片物件
        """
        try:
            with Image.open(image_path) as img:
                # 轉換為 RGB 格式
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 調整圖片大小 (如果太大的話)
                max_size = (2048, 2048)
                if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                    img.thumbnail(max_size, Image.Resampling.LANCZOS)
                    logger.info(f"圖片已調整大小: {img.size}")
                
                return img.copy()
                
        except Exception as e:
            logger.error(f"圖片預處理失敗: {e}")
            return None
    
    def _get_passbook_prompt(self) -> str:
        """取得存摺分析的提示"""
        return """請仔細分析這張台灣存摺圖片，並提取以下資訊：

1. 帳戶名稱 (戶名)
2. 存摺帳號 (完整帳號)

重要說明：
- 帳戶名稱通常會標示為「戶名」、「戶名」或「姓名」
- 台灣存摺帳號格式因銀行而異：
  * 中華郵政：存簿帳號14位（局號7位 + 帳號7位）
  * 一般銀行：帳號10-14位不等，依各家銀行規定
  * 虛擬帳號：通常16位

- 在存摺上帳號可能顯示為：
  * 分開顯示：局號 700-1234，帳號 *********
  * 連續顯示：**************
  * 有空格或連字號分隔：700-1234-*********

- 請提取完整的帳號數字，包含所有數字部分
- 如果看到多組數字，請選擇最完整的帳號
- 移除空格、連字號等分隔符號，只保留數字

請以 JSON 格式回傳結果，格式如下：
{
    "account_name": "帳戶名稱",
    "account_number": "完整存摺帳號",
    "confidence": 0.95
}

只回傳 JSON，不要包含其他文字。"""
    
    def _parse_gemini_response(self, response_text: str) -> Dict[str, Any]:
        """
        解析 Gemini API 回應
        
        Args:
            response_text: API 回應文字
            
        Returns:
            解析後的資料字典
        """
        try:
            # 清理回應文字
            cleaned_text = response_text.strip()
            
            # 移除可能的 markdown 標記
            if cleaned_text.startswith('```json'):
                cleaned_text = cleaned_text[7:]
            if cleaned_text.endswith('```'):
                cleaned_text = cleaned_text[:-3]
            
            cleaned_text = cleaned_text.strip()
            
            # 解析 JSON
            result = json.loads(cleaned_text)
            
            logger.info("成功解析 Gemini 回應")
            return result
            
        except json.JSONDecodeError as e:
            logger.error(f"JSON 解析失敗: {e}")
            logger.error(f"原始回應: {response_text}")
            return self._create_error_result("回應格式解析失敗")
        except Exception as e:
            logger.error(f"回應解析失敗: {e}")
            return self._create_error_result(f"回應解析錯誤: {str(e)}")
    
    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """建立錯誤結果"""
        return {
            'success': False,
            'error': error_message,
            'account_name': None,
            'account_number': None,
            'confidence': 0.0,
            'timestamp': datetime.now().isoformat()
        }
    
    def _validate_extracted_data(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        驗證提取的資料

        Args:
            result: 提取的原始資料

        Returns:
            驗證後的資料
        """
        validated_result = {
            'success': True,
            'account_name': result.get('account_name'),
            'account_number': result.get('account_number'),
            'confidence': float(result.get('confidence', 0.0)),
            'timestamp': datetime.now().isoformat()
        }

        # 檢查必要欄位
        if not validated_result['account_name'] and not validated_result['account_number']:
            validated_result['success'] = False
            validated_result['error'] = '無法提取帳戶名稱或存摺帳號'
            return validated_result

        # 驗證帳號格式
        account_number = validated_result.get('account_number')
        if account_number:
            # 移除可能的空格和特殊字符
            clean_account = ''.join(filter(str.isdigit, str(account_number)))
            validated_result['account_number'] = clean_account

            # 檢查帳號長度並提供相應的狀態資訊
            account_length = len(clean_account)

            if account_length < 10:
                validated_result['account_number_warning'] = f'帳號長度過短 (目前{account_length}碼)，可能為部分帳號'
                validated_result['confidence'] = max(0.0, validated_result['confidence'] - 0.3)
            elif account_length == 10:
                validated_result['account_number_status'] = '10碼帳號 (一般銀行常見格式)'
            elif account_length == 11:
                validated_result['account_number_status'] = '11碼帳號 (一般銀行常見格式)'
            elif account_length == 12:
                validated_result['account_number_status'] = '12碼帳號 (一般銀行常見格式)'
            elif account_length == 13:
                validated_result['account_number_status'] = '13碼帳號 (一般銀行常見格式)'
            elif account_length == 14:
                validated_result['account_number_status'] = '14碼帳號 (郵局存簿或一般銀行格式)'
            elif account_length == 16:
                validated_result['account_number_status'] = '16碼帳號 (虛擬帳號格式)'
            elif account_length > 16:
                validated_result['account_number_warning'] = f'帳號長度過長 (目前{account_length}碼)，請確認是否正確'
                validated_result['confidence'] = max(0.0, validated_result['confidence'] - 0.2)
            else:
                validated_result['account_number_status'] = f'{account_length}碼帳號'

        return validated_result
    
    def extract_passbook_info(self, image_path: str) -> Dict[str, Any]:
        """
        從存摺圖片中提取資訊
        
        Args:
            image_path: 存摺圖片路徑
            
        Returns:
            提取的存摺資訊
        """
        logger.info(f"開始分析存摺: {image_path}")
        
        if not self.gemini_model:
            return self._create_error_result("未設定 Gemini API 金鑰")
        
        # 驗證圖片
        if not self.validate_image(image_path):
            return self._create_error_result("圖片驗證失敗")
        
        # 預處理圖片
        processed_img = self.preprocess_image(image_path)
        if not processed_img:
            return self._create_error_result("圖片預處理失敗")
        
        try:
            # 取得分析提示
            prompt = self._get_passbook_prompt()
            
            # 呼叫 Gemini API
            response = self.gemini_model.generate_content([prompt, processed_img])
            
            if not response.text:
                return self._create_error_result("Gemini API 未返回結果")
            
            logger.info("Gemini API 分析完成")
            
            # 解析回應
            result = self._parse_gemini_response(response.text)
            
            if result.get('success') is False:
                return result
            
            # 驗證提取的資料
            validated_result = self._validate_extracted_data(result)
            
            # 加入額外資訊
            validated_result['image_path'] = image_path
            validated_result['source'] = 'gemini'
            
            logger.info(f"存摺分析完成: {validated_result.get('account_name', 'Unknown')}")
            return validated_result
            
        except Exception as e:
            logger.error(f"存摺分析失敗: {e}")
            return self._create_error_result(f"分析過程發生錯誤: {str(e)}")
    
    def save_result(self, result: Dict[str, Any], output_file: Optional[str] = None) -> str:
        """
        儲存分析結果
        
        Args:
            result: 分析結果
            output_file: 輸出檔案名稱
            
        Returns:
            儲存的檔案路徑
        """
        try:
            if not output_file:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = f"passbook_result_{timestamp}.json"
            
            # 確保輸出目錄存在
            os.makedirs('results', exist_ok=True)
            output_path = os.path.join('results', output_file)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            logger.info(f"結果已儲存至: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"儲存結果失敗: {e}")
            return ""


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='存摺分析器')
    parser.add_argument('image_path', help='存摺圖片路徑')
    parser.add_argument('--output', '-o', help='輸出檔案名稱')
    
    args = parser.parse_args()
    
    try:
        # 載入環境變數
        from dotenv import load_dotenv
        load_dotenv()
    except ImportError:
        print("⚠️  python-dotenv 未安裝，跳過 .env 檔案載入")
    
    # 建立分析器
    analyzer = PassbookAnalyzer()
    
    # 執行分析
    result = analyzer.extract_passbook_info(args.image_path)
    
    # 儲存結果
    output_file = analyzer.save_result(result, args.output)
    
    # 顯示結果
    print("=" * 50)
    print("存摺分析結果")
    print("=" * 50)
    
    if result.get('success'):
        print(f"帳戶名稱: {result.get('account_name', 'N/A')}")
        print(f"存摺帳號: {result.get('account_number', 'N/A')}")
        print(f"信心度: {result.get('confidence', 0):.2%}")
    else:
        print(f"分析失敗: {result.get('error', 'Unknown error')}")
    
    print(f"\n結果已儲存至: {output_file}")
